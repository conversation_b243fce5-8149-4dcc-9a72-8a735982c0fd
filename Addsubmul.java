class Addsubmul {
    public static void main(String[] args) {
        // Initialize two 2D arrays for operations
        int[][] array1 = {
            {10, 20, 30},
            {40, 50, 60},
            {70, 80, 90}
        };

        int[][] array2 = {
            {1, 2, 3},
            {4, 5, 6},
            {7, 8, 9}
        };

        // Get dimensions
        int rows = array1.length;
        int cols = array1[0].length;

        // Create result arrays
        int[][] addition = new int[rows][cols];
        int[][] multiplication = new int[rows][cols];
        double[][] division = new double[rows][cols];

        // Perform operations
        System.out.println("=== 2D ARRAY OPERATIONS ===\n");

        // Display original arrays
        System.out.println("Array 1:");
        displayIntArray(array1);

        System.out.println("\nArray 2:");
        displayIntArray(array2);

        // Addition
        System.out.println("\n=== ADDITION ===");
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                addition[i][j] = array1[i][j] + array2[i][j];
            }
        }
        System.out.println("Array1 + Array2:");
        displayIntArray(addition);

        // Multiplication
        System.out.println("\n=== MULTIPLICATION ===");
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                multiplication[i][j] = array1[i][j] * array2[i][j];
            }
        }
        System.out.println("Array1 * Array2:");
        displayIntArray(multiplication);

        // Division
        System.out.println("\n=== DIVISION ===");
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                if (array2[i][j] != 0) {
                    division[i][j] = (double) array1[i][j] / array2[i][j];
                } else {
                    division[i][j] = Double.POSITIVE_INFINITY;
                }
            }
        }
        System.out.println("Array1 / Array2:");
        displayDoubleArray(division);
    }

    // Method to display integer 2D array
    public static void displayIntArray(int[][] array) {
        for (int i = 0; i < array.length; i++) {
            for (int j = 0; j < array[i].length; j++) {
                System.out.printf("%8d ", array[i][j]);
            }
            System.out.println();
        }
    }

    // Method to display double 2D array
    public static void displayDoubleArray(double[][] array) {
        for (int i = 0; i < array.length; i++) {
            for (int j = 0; j < array[i].length; j++) {
                System.out.printf("%8.2f ", array[i][j]);
            }
            System.out.println();
        }
    }
}